<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' : ''; ?>Content Grabber</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/style.css">
</head>
<body>
    <!-- Modern Navigation Bar -->
    <nav class="modern-navbar">
        <div class="navbar-container">
            <!-- Brand Section -->
            <div class="navbar-brand-section">
                <a class="modern-navbar-brand" href="<?php echo BASE_URL; ?>">
                    <div class="brand-icon">
                        <i class="fas fa-download"></i>
                    </div>
                    <div class="brand-text">
                        <span class="brand-name">Content Grabber</span>
                        <span class="brand-tagline">AI-Powered Content Acquisition</span>
                    </div>
                </a>
            </div>

            <!-- Mobile Toggle -->
            <button class="mobile-toggle" type="button" data-bs-toggle="collapse" data-bs-target="#modernNavbar" aria-controls="modernNavbar" aria-expanded="false" aria-label="Toggle navigation">
                <span class="toggle-line"></span>
                <span class="toggle-line"></span>
                <span class="toggle-line"></span>
            </button>

            <!-- Navigation Links -->
            <div class="collapse navbar-collapse" id="modernNavbar">
                <div class="navbar-nav-section">
                    <ul class="modern-nav-list">
                        <li class="nav-item">
                            <a class="modern-nav-link <?php echo $page === 'dashboard' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>">
                                <div class="nav-icon">
                                    <i class="fas fa-home"></i>
                                </div>
                                <span class="nav-text">Dashboard</span>
                                <div class="nav-indicator"></div>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="modern-nav-link <?php echo $page === 'jobs' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/?page=jobs">
                                <div class="nav-icon">
                                    <i class="fas fa-rocket"></i>
                                </div>
                                <span class="nav-text">Jobs</span>
                                <div class="nav-indicator"></div>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="modern-nav-link <?php echo $page === 'posts' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/?page=posts">
                                <div class="nav-icon">
                                    <i class="fas fa-newspaper"></i>
                                </div>
                                <span class="nav-text">Posts</span>
                                <div class="nav-indicator"></div>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="modern-nav-link <?php echo $page === 'analytics' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/?page=analytics">
                                <div class="nav-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <span class="nav-text">Analytics</span>
                                <div class="nav-indicator"></div>
                            </a>
                        </li>
                        <?php
                        // Check if user has permission to manage users
                        try {
                            if (file_exists('includes/UserManager.php')) {
                                require_once 'includes/UserManager.php';
                                $userManager = new UserManager($db);
                                // Check if user is logged in and has permission
                                if (isset($_SESSION['user_id']) && $userManager->hasPermission($_SESSION['user_id'], 'manage_users')):
                                ?>
                                <li class="nav-item">
                                    <a class="modern-nav-link <?php echo $page === 'users' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/?page=users">
                                        <div class="nav-icon">
                                            <i class="fas fa-users"></i>
                                        </div>
                                        <span class="nav-text">Users</span>
                                        <div class="nav-indicator"></div>
                                    </a>
                                </li>
                                <?php
                                endif;
                            }
                        } catch (Exception $e) {
                            // Ignore errors if UserManager is not available yet
                        }
                        ?>
                        <li class="nav-item">
                            <a class="modern-nav-link <?php echo $page === 'ai_settings' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/?page=ai_settings">
                                <div class="nav-icon">
                                    <i class="fas fa-brain"></i>
                                </div>
                                <span class="nav-text">AI Settings</span>
                                <div class="nav-indicator"></div>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="modern-nav-link <?php echo $page === 'settings' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/?page=settings">
                                <div class="nav-icon">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <span class="nav-text">Settings</span>
                                <div class="nav-indicator"></div>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- User Section -->
                <div class="navbar-user-section">
                    <?php if (isset($_SESSION['user_id']) && isset($currentUser)): ?>
                    <div class="user-dropdown">
                        <button class="user-dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <div class="user-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="user-info">
                                <span class="user-name"><?php echo htmlspecialchars($currentUser['username'] ?? 'User'); ?></span>
                                <span class="user-role">Administrator</span>
                            </div>
                            <div class="dropdown-arrow">
                                <i class="fas fa-chevron-down"></i>
                            </div>
                        </button>
                        <ul class="modern-dropdown-menu dropdown-menu dropdown-menu-end">
                            <li class="dropdown-header">
                                <div class="dropdown-user-info">
                                    <div class="dropdown-avatar">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div>
                                        <div class="dropdown-user-name"><?php echo htmlspecialchars($currentUser['username'] ?? 'User'); ?></div>
                                        <div class="dropdown-user-email"><EMAIL></div>
                                    </div>
                                </div>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="modern-dropdown-item" href="<?php echo BASE_URL; ?>/?page=settings">
                                    <i class="fas fa-user-cog"></i>
                                    <span>Profile Settings</span>
                                </a>
                            </li>
                            <li>
                                <a class="modern-dropdown-item" href="<?php echo BASE_URL; ?>/?page=ai_settings">
                                    <i class="fas fa-brain"></i>
                                    <span>AI Configuration</span>
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="modern-dropdown-item logout-item" href="<?php echo BASE_URL; ?>/logout.php">
                                    <i class="fas fa-sign-out-alt"></i>
                                    <span>Sign Out</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                    <?php else: ?>
                    <a href="<?php echo BASE_URL; ?>/login.php" class="login-btn">
                        <i class="fas fa-sign-in-alt"></i>
                        <span>Sign In</span>
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid px-4 py-4" style="max-width: 1400px; margin: 0 auto;">
        <?php if (isset($_SESSION) && isset($_SESSION['db_update_required']) && $_SESSION['db_update_required']): ?>
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <div class="d-flex align-items-center">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <div>
                    <strong>Database update required!</strong> Your database needs to be updated to the latest version.
                    <div class="mt-2">
                        <a href="<?php echo BASE_URL; ?>/update_db.php" class="btn btn-sm btn-warning">Update Database Now</a>
                        <a href="<?php echo BASE_URL; ?>/?page=settings" class="btn btn-sm btn-outline-secondary ms-2">Go to Settings</a>
                    </div>
                </div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if (isset($pageTitle)): ?>
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="mb-0"><?php echo $pageTitle; ?></h1>
            <?php if (isset($pageActions)): ?>
            <div>
                <?php echo $pageActions; ?>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>
