<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' : ''; ?>Content Grabber</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/style.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="<?php echo BASE_URL; ?>">
                <i class="fas fa-globe me-2"></i>
                Content Grabber
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page === 'dashboard' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>">
                            <i class="fas fa-tachometer-alt me-1"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page === 'jobs' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/?page=jobs">
                            <i class="fas fa-tasks me-1"></i> Jobs
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page === 'posts' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/?page=posts">
                            <i class="fas fa-newspaper me-1"></i> Posts
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page === 'analytics' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/?page=analytics">
                            <i class="fas fa-chart-bar me-1"></i> Analytics
                        </a>
                    </li>
                    <?php
                    // Check if user has permission to manage users
                    try {
                        if (file_exists('includes/UserManager.php')) {
                            require_once 'includes/UserManager.php';
                            $userManager = new UserManager($db);
                            // Check if user is logged in and has permission
                            if (isset($_SESSION['user_id']) && $userManager->hasPermission($_SESSION['user_id'], 'manage_users')):
                            ?>
                            <li class="nav-item">
                                <a class="nav-link <?php echo $page === 'users' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/?page=users">
                                    <i class="fas fa-users me-1"></i> Users
                                </a>
                            </li>
                            <?php
                            endif;
                        }
                    } catch (Exception $e) {
                        // Ignore errors if UserManager is not available yet
                    }
                    ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page === 'ai_settings' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/?page=ai_settings">
                            <i class="fas fa-brain me-1"></i> AI Settings
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page === 'settings' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/?page=settings">
                            <i class="fas fa-cog me-1"></i> Settings
                        </a>
                    </li>
                </ul>
                <div class="d-flex">
                    <?php if (isset($_SESSION['user_id']) && isset($currentUser)): ?>
                    <div class="dropdown">
                        <button class="btn btn-light dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user me-1"></i> <?php echo htmlspecialchars($currentUser['username'] ?? 'User'); ?>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/?page=settings"><i class="fas fa-user-cog me-1"></i> Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/logout.php"><i class="fas fa-sign-out-alt me-1"></i> Logout</a></li>
                        </ul>
                    </div>
                    <?php else: ?>
                    <a href="<?php echo BASE_URL; ?>/login.php" class="btn btn-light">
                        <i class="fas fa-sign-in-alt me-1"></i> Login
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <?php if (isset($_SESSION) && isset($_SESSION['db_update_required']) && $_SESSION['db_update_required']): ?>
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <div class="d-flex align-items-center">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <div>
                    <strong>Database update required!</strong> Your database needs to be updated to the latest version.
                    <div class="mt-2">
                        <a href="<?php echo BASE_URL; ?>/update_db.php" class="btn btn-sm btn-warning">Update Database Now</a>
                        <a href="<?php echo BASE_URL; ?>/?page=settings" class="btn btn-sm btn-outline-secondary ms-2">Go to Settings</a>
                    </div>
                </div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if (isset($pageTitle)): ?>
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="mb-0"><?php echo $pageTitle; ?></h1>
            <?php if (isset($pageActions)): ?>
            <div>
                <?php echo $pageActions; ?>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>
