/* Modern Content Grabber UI */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600&display=swap');

/* Advanced CSS Variables for Design System */
:root {
    /* Primary Colors - Modern Blue Palette */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;

    /* Semantic Colors */
    --success-50: #ecfdf5;
    --success-500: #10b981;
    --success-600: #059669;
    --success-700: #047857;

    --warning-50: #fffbeb;
    --warning-500: #f59e0b;
    --warning-600: #d97706;
    --warning-700: #b45309;

    --danger-50: #fef2f2;
    --danger-500: #ef4444;
    --danger-600: #dc2626;
    --danger-700: #b91c1c;

    --info-50: #f0f9ff;
    --info-500: #06b6d4;
    --info-600: #0891b2;
    --info-700: #0e7490;

    /* Neutral Colors */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* Semantic Mappings */
    --primary-color: var(--primary-500);
    --primary-dark: var(--primary-600);
    --primary-light: var(--primary-100);
    --text-primary: var(--gray-900);
    --text-secondary: var(--gray-600);
    --text-muted: var(--gray-400);
    --border-color: var(--gray-200);
    --background-primary: var(--gray-50);
    --background-secondary: #ffffff;

    /* Shadows */
    --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);

    /* Border Radius */
    --radius-xs: 0.25rem;
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;

    /* Spacing Scale */
    --space-px: 1px;
    --space-0: 0;
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    --space-24: 6rem;

    /* Typography */
    --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-family-mono: 'JetBrains Mono', 'Fira Code', Consolas, 'Liberation Mono', Menlo, Courier, monospace;

    /* Font Sizes */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;
    --text-6xl: 3.75rem;

    /* Line Heights */
    --leading-none: 1;
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;

    /* Transitions */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-base: 200ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);

    /* Z-Index Scale */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* Reset and Base Styles */
*,
*::before,
*::after {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
    line-height: var(--leading-normal);
}

body {
    margin: 0;
    padding: 0;
    font-family: var(--font-family-sans);
    font-size: var(--text-base);
    font-weight: 400;
    line-height: var(--leading-normal);
    color: var(--text-primary);
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Enhanced Background with Subtle Pattern */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(16, 185, 129, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* Modern Card System */
.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background: var(--background-secondary);
    background-clip: border-box;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-base);
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
    border-color: var(--primary-200);
}

.card-elevated {
    box-shadow: var(--shadow-lg);
}

.card-elevated:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-4px);
}

.card-glass {
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: var(--shadow-lg);
}

.card-gradient {
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
    color: white;
    border: none;
}

.card-gradient .card-header {
    background: rgba(255, 255, 255, 0.1);
    border-bottom-color: rgba(255, 255, 255, 0.2);
    color: white;
}

.card-header {
    padding: var(--space-6);
    margin-bottom: 0;
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--background-secondary) 100%);
    border-bottom: 1px solid var(--border-color);
    border-top-left-radius: calc(var(--radius-xl) - 1px);
    border-top-right-radius: calc(var(--radius-xl) - 1px);
    font-weight: 600;
    font-size: var(--text-lg);
    color: var(--text-primary);
}

.card-header:first-child {
    border-radius: calc(var(--radius-xl) - 1px) calc(var(--radius-xl) - 1px) 0 0;
}

.card-body {
    flex: 1 1 auto;
    padding: var(--space-6);
    color: var(--text-primary);
}

.card-footer {
    padding: var(--space-4) var(--space-6);
    background: var(--gray-50);
    border-top: 1px solid var(--border-color);
    border-bottom-left-radius: calc(var(--radius-xl) - 1px);
    border-bottom-right-radius: calc(var(--radius-xl) - 1px);
}

.card-footer:last-child {
    border-radius: 0 0 calc(var(--radius-xl) - 1px) calc(var(--radius-xl) - 1px);
}

/* Card Variants */
.card-primary {
    border-color: var(--primary-200);
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--background-secondary) 100%);
}

.card-success {
    border-color: var(--success-500);
    background: linear-gradient(135deg, var(--success-50) 0%, var(--background-secondary) 100%);
}

.card-warning {
    border-color: var(--warning-500);
    background: linear-gradient(135deg, var(--warning-50) 0%, var(--background-secondary) 100%);
}

.card-danger {
    border-color: var(--danger-500);
    background: linear-gradient(135deg, var(--danger-50) 0%, var(--background-secondary) 100%);
}

.card-info {
    border-color: var(--info-500);
    background: linear-gradient(135deg, var(--info-50) 0%, var(--background-secondary) 100%);
}

/* Modern Button System */
.btn {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    font-family: var(--font-family-sans);
    font-size: var(--text-sm);
    font-weight: 600;
    line-height: var(--leading-tight);
    text-align: center;
    text-decoration: none;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    border: 1px solid transparent;
    border-radius: var(--radius-lg);
    transition: all var(--transition-base);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.12);
}

.btn:disabled,
.btn.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Button Variants */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
    border-color: var(--primary-500);
    color: white;
    box-shadow: var(--shadow-sm), 0 1px 0 rgba(255, 255, 255, 0.15) inset;
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
    border-color: var(--primary-600);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md), 0 1px 0 rgba(255, 255, 255, 0.15) inset;
    color: white;
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm), 0 1px 0 rgba(0, 0, 0, 0.1) inset;
}

.btn-success {
    background: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%);
    border-color: var(--success-500);
    color: white;
    box-shadow: var(--shadow-sm), 0 1px 0 rgba(255, 255, 255, 0.15) inset;
}

.btn-success:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--success-600) 0%, var(--success-700) 100%);
    border-color: var(--success-600);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md), 0 1px 0 rgba(255, 255, 255, 0.15) inset;
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-600) 100%);
    border-color: var(--warning-500);
    color: white;
    box-shadow: var(--shadow-sm), 0 1px 0 rgba(255, 255, 255, 0.15) inset;
}

.btn-warning:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--warning-600) 0%, var(--warning-700) 100%);
    border-color: var(--warning-600);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md), 0 1px 0 rgba(255, 255, 255, 0.15) inset;
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-500) 0%, var(--danger-600) 100%);
    border-color: var(--danger-500);
    color: white;
    box-shadow: var(--shadow-sm), 0 1px 0 rgba(255, 255, 255, 0.15) inset;
}

.btn-danger:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--danger-600) 0%, var(--danger-700) 100%);
    border-color: var(--danger-600);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md), 0 1px 0 rgba(255, 255, 255, 0.15) inset;
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, var(--info-500) 0%, var(--info-600) 100%);
    border-color: var(--info-500);
    color: white;
    box-shadow: var(--shadow-sm), 0 1px 0 rgba(255, 255, 255, 0.15) inset;
}

.btn-info:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--info-600) 0%, var(--info-700) 100%);
    border-color: var(--info-600);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md), 0 1px 0 rgba(255, 255, 255, 0.15) inset;
    color: white;
}

.btn-light {
    background: var(--background-secondary);
    border-color: var(--border-color);
    color: var(--text-primary);
    box-shadow: var(--shadow-sm);
}

.btn-light:hover:not(:disabled) {
    background: var(--gray-50);
    border-color: var(--gray-300);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    color: var(--text-primary);
}

.btn-dark {
    background: linear-gradient(135deg, var(--gray-800) 0%, var(--gray-900) 100%);
    border-color: var(--gray-800);
    color: white;
    box-shadow: var(--shadow-sm), 0 1px 0 rgba(255, 255, 255, 0.15) inset;
}

.btn-dark:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-900) 100%);
    border-color: var(--gray-900);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md), 0 1px 0 rgba(255, 255, 255, 0.15) inset;
    color: white;
}

/* Outline Buttons */
.btn-outline-primary {
    background: transparent;
    border-color: var(--primary-500);
    color: var(--primary-600);
    box-shadow: none;
}

.btn-outline-primary:hover:not(:disabled) {
    background: var(--primary-500);
    border-color: var(--primary-500);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-outline-secondary {
    background: transparent;
    border-color: var(--gray-300);
    color: var(--text-secondary);
    box-shadow: none;
}

.btn-outline-secondary:hover:not(:disabled) {
    background: var(--gray-500);
    border-color: var(--gray-500);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Button Sizes */
.btn-xs {
    padding: var(--space-1) var(--space-3);
    font-size: var(--text-xs);
    border-radius: var(--radius-md);
}

.btn-sm {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-sm);
    border-radius: var(--radius-md);
}

.btn-lg {
    padding: var(--space-4) var(--space-8);
    font-size: var(--text-lg);
    border-radius: var(--radius-xl);
}

.btn-xl {
    padding: var(--space-5) var(--space-10);
    font-size: var(--text-xl);
    border-radius: var(--radius-xl);
}

/* Button Groups */
.btn-group {
    position: relative;
    display: inline-flex;
    vertical-align: middle;
}

.btn-group > .btn {
    position: relative;
    flex: 1 1 auto;
    border-radius: 0;
    margin-left: -1px;
}

.btn-group > .btn:first-child {
    margin-left: 0;
    border-top-left-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-lg);
}

.btn-group > .btn:last-child {
    border-top-right-radius: var(--radius-lg);
    border-bottom-right-radius: var(--radius-lg);
}

.btn-group > .btn:hover {
    z-index: 1;
}

/* Modern Navbar System */
.navbar {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%) !important;
    backdrop-filter: blur(20px) saturate(180%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow-lg);
    padding: var(--space-4) 0;
    position: relative;
    z-index: var(--z-sticky);
}

.navbar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    pointer-events: none;
}

.navbar-brand {
    font-weight: 800;
    font-size: var(--text-2xl);
    color: white !important;
    display: flex;
    align-items: center;
    gap: var(--space-3);
    text-decoration: none;
    transition: all var(--transition-base);
    position: relative;
    z-index: 1;
}

.navbar-brand:hover {
    transform: scale(1.02);
    color: white !important;
}

.navbar-brand i {
    font-size: var(--text-3xl);
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.navbar-nav {
    gap: var(--space-1);
}

.nav-link {
    font-weight: 500;
    font-size: var(--text-sm);
    color: rgba(255, 255, 255, 0.85) !important;
    transition: all var(--transition-base);
    border-radius: var(--radius-lg);
    padding: var(--space-2) var(--space-4) !important;
    margin: 0 var(--space-1);
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    opacity: 0;
    transition: opacity var(--transition-base);
}

.nav-link:hover::before {
    opacity: 1;
}

.nav-link:hover {
    color: white !important;
    transform: translateY(-1px);
}

.nav-link.active {
    color: white !important;
    background: rgba(255, 255, 255, 0.15);
    box-shadow: var(--shadow-inner);
    font-weight: 600;
}

.nav-link.active::before {
    opacity: 0;
}

.navbar-toggler {
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    padding: var(--space-2) var(--space-3);
    transition: all var(--transition-base);
}

.navbar-toggler:hover {
    border-color: rgba(255, 255, 255, 0.4);
    background: rgba(255, 255, 255, 0.1);
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.2);
    outline: none;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.85%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Dropdown Menus */
.dropdown-menu {
    background: var(--background-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    padding: var(--space-2);
    margin-top: var(--space-2);
    backdrop-filter: blur(20px);
    min-width: 200px;
}

.dropdown-item {
    border-radius: var(--radius-lg);
    padding: var(--space-3) var(--space-4);
    font-size: var(--text-sm);
    font-weight: 500;
    color: var(--text-primary);
    transition: all var(--transition-base);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.dropdown-item:hover {
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);
    color: var(--primary-700);
    transform: translateX(2px);
}

.dropdown-item:active {
    background: var(--primary-100);
    color: var(--primary-800);
}

.dropdown-divider {
    height: 1px;
    margin: var(--space-2) 0;
    background: linear-gradient(90deg, transparent, var(--border-color), transparent);
    border: none;
}

/* Table styles */
.table th {
    font-weight: 600;
    color: #555;
}

.table-hover tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.05);
}

/* Form styles */
.form-label {
    font-weight: 500;
    color: #555;
}

.form-control:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.25rem rgba(52, 152, 219, 0.25);
}

/* Alert styles */
.alert {
    border-radius: 10px;
    border: none;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

/* Badge styles */
.badge {
    font-weight: 500;
    padding: 5px 10px;
    border-radius: 5px;
}

/* Footer styles */
footer {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    color: #6c757d;
}

/* Content styles */
.content img {
    max-width: 100%;
    height: auto;
    border-radius: 5px;
    margin: 10px 0;
}

.content h1, .content h2, .content h3, .content h4, .content h5, .content h6 {
    margin-top: 20px;
    margin-bottom: 10px;
    color: #2c3e50;
}

.content p {
    margin-bottom: 15px;
    line-height: 1.6;
}

.content blockquote {
    border-left: 4px solid #3498db;
    padding-left: 15px;
    margin-left: 0;
    color: #555;
}

.content ul, .content ol {
    margin-bottom: 15px;
    padding-left: 20px;
}

.content figure {
    margin: 20px 0;
    text-align: center;
}

.content figcaption {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 5px;
}

/* Grid view styles */
.card-img-top {
    height: 200px;
    object-fit: cover;
}

/* Dashboard stats */
.display-4 {
    font-size: 3rem;
    color: #3498db;
}

/* Quick actions */
.btn-primary .fa-2x, .btn-success .fa-2x, .btn-info .fa-2x, .btn-secondary .fa-2x {
    color: rgba(255, 255, 255, 0.8);
}

/* Process page */
.modal-xl .modal-content {
    min-height: 500px;
}

/* Modern Form System */
.form-control,
.form-select {
    display: block;
    width: 100%;
    padding: var(--space-3) var(--space-4);
    font-family: var(--font-family-sans);
    font-size: var(--text-sm);
    font-weight: 400;
    line-height: var(--leading-normal);
    color: var(--text-primary);
    background: var(--background-secondary);
    background-clip: padding-box;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    transition: all var(--transition-base);
    box-shadow: var(--shadow-xs);
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-500);
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.12), var(--shadow-sm);
    background: var(--background-secondary);
}

.form-control:disabled,
.form-select:disabled {
    background: var(--gray-50);
    border-color: var(--gray-200);
    color: var(--text-muted);
    cursor: not-allowed;
}

.form-control::placeholder {
    color: var(--text-muted);
    opacity: 1;
}

.form-label {
    display: inline-block;
    margin-bottom: var(--space-2);
    font-weight: 600;
    font-size: var(--text-sm);
    color: var(--text-primary);
    line-height: var(--leading-tight);
}

.form-text {
    margin-top: var(--space-1);
    font-size: var(--text-xs);
    color: var(--text-muted);
    line-height: var(--leading-normal);
}

/* Input Groups */
.input-group {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: stretch;
    width: 100%;
}

.input-group > .form-control,
.input-group > .form-select {
    position: relative;
    flex: 1 1 auto;
    width: 1%;
    min-width: 0;
}

.input-group-text {
    display: flex;
    align-items: center;
    padding: var(--space-3) var(--space-4);
    font-size: var(--text-sm);
    font-weight: 500;
    line-height: var(--leading-normal);
    color: var(--text-secondary);
    text-align: center;
    white-space: nowrap;
    background: var(--gray-50);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
}

.input-group > .input-group-text:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: 0;
}

.input-group > .form-control:not(:first-child),
.input-group > .form-select:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left: 0;
}

/* Form Validation */
.form-control.is-valid,
.form-select.is-valid {
    border-color: var(--success-500);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.12);
}

.form-control.is-invalid,
.form-select.is-invalid {
    border-color: var(--danger-500);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.12);
}

.valid-feedback {
    display: block;
    width: 100%;
    margin-top: var(--space-1);
    font-size: var(--text-xs);
    color: var(--success-600);
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: var(--space-1);
    font-size: var(--text-xs);
    color: var(--danger-600);
}

/* Checkboxes and Radios */
.form-check {
    display: block;
    min-height: 1.5rem;
    padding-left: 1.5em;
    margin-bottom: var(--space-2);
}

.form-check-input {
    width: 1em;
    height: 1em;
    margin-top: 0.25em;
    margin-left: -1.5em;
    vertical-align: top;
    background: var(--background-secondary);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-sm);
    transition: all var(--transition-base);
}

.form-check-input:checked {
    background-color: var(--primary-500);
    border-color: var(--primary-500);
}

.form-check-input:focus {
    border-color: var(--primary-500);
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.12);
}

.form-check-label {
    color: var(--text-primary);
    font-size: var(--text-sm);
    font-weight: 500;
}

/* Modern table styles */
.table {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table th {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    font-weight: 600;
    color: var(--text-primary);
    border: none;
    padding: 1rem 1.5rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.table td {
    padding: 1rem 1.5rem;
    border: none;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(59, 130, 246, 0.05) 100%);
    transform: scale(1.001);
    transition: all 0.2s ease;
}

/* Modern alert styles */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    font-weight: 500;
    border-left: 4px solid;
}

.alert-success {
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
    color: var(--success-dark);
    border-left-color: var(--success-color);
}

.alert-danger {
    background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
    color: var(--danger-dark);
    border-left-color: var(--danger-color);
}

.alert-warning {
    background: linear-gradient(135deg, #fffbeb 0%, #fed7aa 100%);
    color: var(--warning-dark);
    border-left-color: var(--warning-color);
}

.alert-info {
    background: linear-gradient(135deg, #f0f9ff 0%, #bae6fd 100%);
    color: var(--info-dark);
    border-left-color: var(--info-color);
}

/* Modern badge styles */
.badge {
    font-weight: 600;
    padding: 0.375rem 0.75rem;
    border-radius: var(--border-radius-sm);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.bg-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
}

.bg-success {
    background: linear-gradient(135deg, var(--success-color) 0%, var(--success-dark) 100%) !important;
}

.bg-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, var(--danger-dark) 100%) !important;
}

.bg-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-dark) 100%) !important;
}

.bg-info {
    background: linear-gradient(135deg, var(--info-color) 0%, var(--info-dark) 100%) !important;
}

/* Progress bars */
.progress {
    height: 0.75rem;
    border-radius: var(--border-radius-sm);
    background: var(--light-color);
    overflow: hidden;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    transition: width 0.3s ease;
}

/* Modal styles */
.modal-content {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    backdrop-filter: blur(20px);
}

.modal-header {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-bottom: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    background: var(--light-color);
    border-top: 1px solid var(--border-color);
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
    padding: 1rem 1.5rem;
}

/* Dashboard stats */
.display-4 {
    font-size: 3rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Icon boxes */
.icon-box {
    width: 3rem;
    height: 3rem;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    transition: all 0.2s ease;
}

.icon-box:hover {
    transform: scale(1.1);
}

/* Loading states */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

/* Modern Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-700) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-gradient-success {
    background: linear-gradient(135deg, var(--success-500) 0%, var(--success-700) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-gradient-warning {
    background: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-700) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-gradient-info {
    background: linear-gradient(135deg, var(--info-500) 0%, var(--info-700) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Background Gradients */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-700) 100%) !important;
}

.bg-gradient-success {
    background: linear-gradient(135deg, var(--success-500) 0%, var(--success-700) 100%) !important;
}

.bg-gradient-info {
    background: linear-gradient(135deg, var(--info-500) 0%, var(--info-700) 100%) !important;
}

.bg-gradient-warning {
    background: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-700) 100%) !important;
}

.bg-gradient-danger {
    background: linear-gradient(135deg, var(--danger-500) 0%, var(--danger-700) 100%) !important;
}

/* Interactive Effects */
.hover-lift {
    transition: all var(--transition-base);
}

.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.hover-scale {
    transition: transform var(--transition-base);
}

.hover-scale:hover {
    transform: scale(1.02);
}

.hover-glow {
    transition: all var(--transition-base);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

/* Transition Utilities */
.transition-all {
    transition: all var(--transition-base);
}

.transition-fast {
    transition: all var(--transition-fast);
}

.transition-slow {
    transition: all var(--transition-slow);
}

/* Spacing Utilities */
.tracking-wide {
    letter-spacing: 0.025em;
}

.tracking-wider {
    letter-spacing: 0.05em;
}

.tracking-widest {
    letter-spacing: 0.1em;
}

/* Border Utilities */
.border-opacity-10 {
    border-color: rgba(var(--border-color), 0.1) !important;
}

.border-opacity-25 {
    border-color: rgba(var(--border-color), 0.25) !important;
}

.border-opacity-50 {
    border-color: rgba(var(--border-color), 0.5) !important;
}

/* Background Opacity Utilities */
.bg-opacity-5 {
    background-color: rgba(var(--primary-500), 0.05) !important;
}

.bg-opacity-10 {
    background-color: rgba(var(--primary-500), 0.1) !important;
}

.bg-opacity-20 {
    background-color: rgba(var(--primary-500), 0.2) !important;
}

/* Glass Morphism */
.glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: var(--radius-full);
    transition: background var(--transition-base);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

::-webkit-scrollbar-corner {
    background: var(--gray-100);
}

/* Loading States */
.skeleton {
    background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Enhanced Animations */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.fade-in-down {
    animation: fadeInDown 0.6s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

.zoom-in {
    animation: zoomIn 0.6s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Floating Animation */
.float {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Modern Focus States */
.focus-ring:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.12);
}

.focus-ring-success:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.12);
}

.focus-ring-warning:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.12);
}

.focus-ring-danger:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.12);
}

/* Modern UI Components */

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(15, 23, 42, 0.8);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-base);
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.1);
    border-left: 4px solid var(--primary-500);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

.loading-text {
    font-size: var(--text-lg);
    font-weight: 500;
    margin-top: var(--space-4);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Global Progress Bar */
.global-progress-bar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gray-200);
    z-index: 9998;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-base);
}

.global-progress-bar.show {
    opacity: 1;
    visibility: visible;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
    width: 0%;
    transition: width var(--transition-base);
}

.progress-text {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gray-900);
    color: white;
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    font-size: var(--text-xs);
    white-space: nowrap;
    margin-top: var(--space-1);
}

/* Modern Tooltips */
.modern-tooltip {
    position: absolute;
    background: var(--gray-900);
    color: white;
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-lg);
    font-size: var(--text-sm);
    font-weight: 500;
    z-index: 9999;
    opacity: 0;
    transform: translateY(4px);
    transition: all var(--transition-fast);
    pointer-events: none;
    box-shadow: var(--shadow-lg);
    max-width: 200px;
    text-align: center;
}

.modern-tooltip::before {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: var(--gray-900);
}

.modern-tooltip.show {
    opacity: 1;
    transform: translateY(0);
}

/* Notification System */
.notification-container {
    position: fixed;
    top: var(--space-4);
    right: var(--space-4);
    z-index: 9997;
    max-width: 400px;
    width: 100%;
}

.modern-notification {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    margin-bottom: var(--space-3);
    opacity: 0;
    transform: translateX(100%);
    transition: all var(--transition-base);
    border-left: 4px solid;
}

.modern-notification.show {
    opacity: 1;
    transform: translateX(0);
}

.notification-content {
    display: flex;
    align-items: center;
    padding: var(--space-4);
    gap: var(--space-3);
}

.notification-icon {
    font-size: var(--text-xl);
    flex-shrink: 0;
}

.notification-message {
    flex-grow: 1;
    font-weight: 500;
    color: var(--text-primary);
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--space-1);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
    flex-shrink: 0;
}

.notification-close:hover {
    background: var(--gray-100);
    color: var(--text-primary);
}

/* Notification Types */
.notification-success {
    border-left-color: var(--success-500);
}

.notification-success .notification-icon {
    color: var(--success-500);
}

.notification-error {
    border-left-color: var(--danger-500);
}

.notification-error .notification-icon {
    color: var(--danger-500);
}

.notification-warning {
    border-left-color: var(--warning-500);
}

.notification-warning .notification-icon {
    color: var(--warning-500);
}

.notification-info {
    border-left-color: var(--info-500);
}

.notification-info .notification-icon {
    color: var(--info-500);
}

/* Enhanced Button Loading States */
.btn.loading {
    position: relative;
    color: transparent !important;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    color: white;
}

/* Modern Spinner */
.spinner-modern {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-200);
    border-radius: 50%;
    border-top-color: var(--primary-500);
    animation: spin 1s ease-in-out infinite;
}

.spinner-lg {
    width: 40px;
    height: 40px;
    border-width: 4px;
}

.spinner-sm {
    width: 16px;
    height: 16px;
    border-width: 2px;
}

/* Enhanced Card Interactions */
.card-interactive {
    cursor: pointer;
    transition: all var(--transition-base);
}

.card-interactive:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
}

.card-interactive:active {
    transform: translateY(-4px);
}

/* Modern Data Tables */
.table-modern {
    background: white;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.table-modern thead th {
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    border: none;
    font-weight: 600;
    color: var(--text-primary);
    padding: var(--space-4) var(--space-6);
    font-size: var(--text-sm);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    position: sticky;
    top: 0;
    z-index: 10;
}

.table-modern tbody td {
    padding: var(--space-4) var(--space-6);
    border: none;
    border-bottom: 1px solid var(--gray-100);
    vertical-align: middle;
    transition: all var(--transition-fast);
}

.table-modern tbody tr:hover td {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(59, 130, 246, 0.05) 100%);
}

.table-modern tbody tr:last-child td {
    border-bottom: none;
}

/* Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-indicator::before {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: currentColor;
}

.status-success {
    background: var(--success-50);
    color: var(--success-700);
}

.status-warning {
    background: var(--warning-50);
    color: var(--warning-700);
}

.status-danger {
    background: var(--danger-50);
    color: var(--danger-700);
}

.status-info {
    background: var(--info-50);
    color: var(--info-700);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-img-top {
        height: 150px;
    }

    .display-4 {
        font-size: 2rem;
    }

    .navbar-brand {
        font-size: 1.2rem;
    }

    .btn {
        padding: var(--space-2) var(--space-4);
        font-size: var(--text-sm);
    }

    .card-body {
        padding: var(--space-4);
    }

    .table th, .table td {
        padding: var(--space-3) var(--space-4);
    }

    .notification-container {
        top: var(--space-2);
        right: var(--space-2);
        left: var(--space-2);
        max-width: none;
    }

    .modern-notification {
        margin-bottom: var(--space-2);
    }

    .notification-content {
        padding: var(--space-3);
    }
}

@media (max-width: 576px) {
    .container {
        padding: 0 1rem;
    }

    .card {
        margin-bottom: 1rem;
    }

    .btn-group {
        flex-direction: column;
    }

    .btn-group .btn {
        border-radius: var(--border-radius-sm);
        margin-bottom: 0.25rem;
    }
}
