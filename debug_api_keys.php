<?php
// Load configuration
require_once 'config.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Load required files
require_once 'includes/Database.php';

// Create database instance
$db = new Database();

// Include tool header
include_once 'includes/tool_header.php';
?>

<div class="container py-4">
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white">
            <h5 class="mb-0">Debug API Keys</h5>
        </div>
        <div class="card-body">
            <h6>Database Connection Test</h6>
            <pre>
<?php
// Test database connection
try {
    $testQuery = $db->query("SELECT 1");
    echo "Database connection: " . (!empty($testQuery) ? "Success" : "Failed") . "\n";
} catch (Exception $e) {
    echo "Database connection error: " . $e->getMessage() . "\n";
}
?>
            </pre>

            <h6>Tables Check</h6>
            <pre>
<?php
// Check if ai_providers table exists
$providersTable = $db->query("SHOW TABLES LIKE 'ai_providers'");
echo "ai_providers table exists: " . (!empty($providersTable) ? 'Yes' : 'No') . "\n";

// Check if ai_api_keys table exists
$apiKeysTable = $db->query("SHOW TABLES LIKE 'ai_api_keys'");
echo "ai_api_keys table exists: " . (!empty($apiKeysTable) ? 'Yes' : 'No') . "\n";
?>
            </pre>

            <h6>Providers Data</h6>
            <pre>
<?php
if (!empty($providersTable)) {
    // Get providers
    $providers = $db->query("SELECT * FROM ai_providers");
    echo "Number of providers: " . count($providers) . "\n\n";
    
    if (!empty($providers)) {
        echo "Providers:\n";
        foreach ($providers as $provider) {
            echo "ID: {$provider['id']}, Name: {$provider['name']}, Slug: {$provider['slug']}, Active: {$provider['is_active']}\n";
        }
    } else {
        echo "No providers found in the database.\n";
    }
} else {
    echo "ai_providers table does not exist.\n";
}
?>
            </pre>

            <h6>API Keys Data</h6>
            <pre>
<?php
if (!empty($apiKeysTable)) {
    // Get API keys directly
    $apiKeys = $db->query("SELECT * FROM ai_api_keys");
    echo "Number of API keys (direct query): " . count($apiKeys) . "\n\n";
    
    if (!empty($apiKeys)) {
        echo "API Keys (direct query):\n";
        foreach ($apiKeys as $key) {
            echo "ID: {$key['id']}, Provider ID: {$key['provider_id']}, Name: {$key['name']}, Default: {$key['is_default']}, Active: {$key['is_active']}\n";
        }
    } else {
        echo "No API keys found in the database (direct query).\n";
    }
    
    echo "\n";
    
    // Get API keys with join
    $apiKeysWithJoin = $db->query("
        SELECT k.*, p.name as provider_name
        FROM ai_api_keys k
        JOIN ai_providers p ON k.provider_id = p.id
        ORDER BY p.name, k.name
    ");
    echo "Number of API keys (with join): " . count($apiKeysWithJoin) . "\n\n";
    
    if (!empty($apiKeysWithJoin)) {
        echo "API Keys (with join):\n";
        foreach ($apiKeysWithJoin as $key) {
            echo "ID: {$key['id']}, Provider: {$key['provider_name']}, Name: {$key['name']}, Default: {$key['is_default']}, Active: {$key['is_active']}\n";
        }
    } else {
        echo "No API keys found in the database (with join).\n";
    }
} else {
    echo "ai_api_keys table does not exist.\n";
}
?>
            </pre>

            <h6>Test Insert API Key</h6>
            <pre>
<?php
if (!empty($providersTable) && !empty($apiKeysTable)) {
    // Get first provider ID
    $providerId = $db->getValue("SELECT id FROM ai_providers LIMIT 1");
    
    if ($providerId) {
        echo "Found provider ID: $providerId\n\n";
        
        // Test insert
        try {
            $testKeyName = "Test Key " . date('Y-m-d H:i:s');
            $testApiKey = "test_key_" . time();
            
            $keyId = $db->insert('ai_api_keys', [
                'provider_id' => $providerId,
                'name' => $testKeyName,
                'api_key' => $testApiKey,
                'is_default' => 0,
                'is_active' => 1
            ]);
            
            echo "Test API key inserted with ID: $keyId\n";
            
            // Verify the key was inserted
            $insertedKey = $db->getRow("SELECT * FROM ai_api_keys WHERE id = ?", [$keyId]);
            
            if ($insertedKey) {
                echo "Successfully verified inserted key:\n";
                echo "ID: {$insertedKey['id']}, Provider ID: {$insertedKey['provider_id']}, Name: {$insertedKey['name']}\n";
                
                // Clean up - delete the test key
                $db->delete('ai_api_keys', 'id = ?', [$keyId]);
                echo "Test key deleted.\n";
            } else {
                echo "Failed to verify inserted key.\n";
            }
        } catch (Exception $e) {
            echo "Error inserting test API key: " . $e->getMessage() . "\n";
        }
    } else {
        echo "No providers found to test API key insertion.\n";
    }
} else {
    echo "Required tables do not exist.\n";
}
?>
            </pre>
        </div>
        <div class="card-footer bg-white">
            <a href="<?php echo BASE_URL; ?>/?page=ai_settings&section=api_keys" class="btn btn-primary">
                <i class="fas fa-arrow-left me-1"></i> Back to API Keys
            </a>
            <a href="<?php echo BASE_URL; ?>/update_ai_tables.php" class="btn btn-outline-primary ms-2">
                <i class="fas fa-sync-alt me-1"></i> Update AI Tables
            </a>
        </div>
    </div>
</div>

<?php
// Include tool footer
include_once 'includes/tool_footer.php';
?>
