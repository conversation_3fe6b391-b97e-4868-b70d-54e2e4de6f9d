<?php
// Load configuration
require_once 'config.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Load required files
require_once 'includes/Database.php';

// Create database instance
$db = new Database();

// Include tool header
include_once 'includes/tool_header.php';
?>

<div class="container py-4">
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white">
            <h5 class="mb-0">Database Tables Check</h5>
        </div>
        <div class="card-body">
            <pre>
<?php
// Check if ai_providers table exists
$providersTable = $db->query("SHOW TABLES LIKE 'ai_providers'");
echo "ai_providers table exists: " . (!empty($providersTable) ? 'Yes' : 'No') . "\n";

if (!empty($providersTable)) {
    // Get providers count
    $providersCount = $db->getValue("SELECT COUNT(*) FROM ai_providers");
    echo "Number of providers: $providersCount\n";
    
    // Get providers
    $providers = $db->query("SELECT * FROM ai_providers");
    echo "Providers:\n";
    print_r($providers);
}

// Check if ai_api_keys table exists
$apiKeysTable = $db->query("SHOW TABLES LIKE 'ai_api_keys'");
echo "\nai_api_keys table exists: " . (!empty($apiKeysTable) ? 'Yes' : 'No') . "\n";

if (!empty($apiKeysTable)) {
    // Get structure
    echo "ai_api_keys table structure:\n";
    $structure = $db->query("DESCRIBE ai_api_keys");
    print_r($structure);
    
    // Get API keys count
    $apiKeysCount = $db->getValue("SELECT COUNT(*) FROM ai_api_keys");
    echo "Number of API keys: $apiKeysCount\n";
    
    // Get API keys
    $apiKeys = $db->query("SELECT * FROM ai_api_keys");
    echo "API keys:\n";
    print_r($apiKeys);
}

// Check if ai_models table exists
$modelsTable = $db->query("SHOW TABLES LIKE 'ai_models'");
echo "\nai_models table exists: " . (!empty($modelsTable) ? 'Yes' : 'No') . "\n";

if (!empty($modelsTable)) {
    // Get models count
    $modelsCount = $db->getValue("SELECT COUNT(*) FROM ai_models");
    echo "Number of models: $modelsCount\n";
}

// Check if ai_workflows table exists
$workflowsTable = $db->query("SHOW TABLES LIKE 'ai_workflows'");
echo "\nai_workflows table exists: " . (!empty($workflowsTable) ? 'Yes' : 'No') . "\n";

if (!empty($workflowsTable)) {
    // Get workflows count
    $workflowsCount = $db->getValue("SELECT COUNT(*) FROM ai_workflows");
    echo "Number of workflows: $workflowsCount\n";
}

// Check if ai_workflow_steps table exists
$workflowStepsTable = $db->query("SHOW TABLES LIKE 'ai_workflow_steps'");
echo "\nai_workflow_steps table exists: " . (!empty($workflowStepsTable) ? 'Yes' : 'No') . "\n";

if (!empty($workflowStepsTable)) {
    // Get workflow steps count
    $workflowStepsCount = $db->getValue("SELECT COUNT(*) FROM ai_workflow_steps");
    echo "Number of workflow steps: $workflowStepsCount\n";
}

// Test insert into ai_api_keys
echo "\nTesting insert into ai_api_keys:\n";
try {
    // Insert test API key
    $testKeyId = $db->insert('ai_api_keys', [
        'provider_id' => 1,
        'name' => 'Test API Key',
        'api_key' => 'test_api_key_' . time(),
        'is_default' => 0,
        'is_active' => 1
    ]);
    
    echo "Test API key inserted with ID: $testKeyId\n";
    
    // Get the inserted key
    $testKey = $db->getRow("SELECT * FROM ai_api_keys WHERE id = ?", [$testKeyId]);
    echo "Inserted key:\n";
    print_r($testKey);
    
    // Delete the test key
    $db->delete('ai_api_keys', 'id = ?', [$testKeyId]);
    echo "Test API key deleted\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
            </pre>
        </div>
        <div class="card-footer bg-white">
            <a href="<?php echo BASE_URL; ?>/?page=ai_settings" class="btn btn-primary">
                <i class="fas fa-cog me-1"></i> Go to AI Settings
            </a>
        </div>
    </div>
</div>

<?php
// Include tool footer
include_once 'includes/tool_footer.php';
?>
