<?php
// Set page title
$pageTitle = 'Posts';

// Set page actions
$pageActions = '<div class="btn-group">
    <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
        <i class="fas fa-filter me-1"></i> Filter
    </button>
    <ul class="dropdown-menu dropdown-menu-end">
        <li><a class="dropdown-item" href="' . BASE_URL . '/?page=posts">All Posts</a></li>
        <li><hr class="dropdown-divider"></li>
        <li><h6 class="dropdown-header">By Job</h6></li>';

// Get all jobs for filter
$jobs = $db->query("SELECT id, name FROM jobs ORDER BY name");
foreach ($jobs as $job) {
    $pageActions .= '<li><a class="dropdown-item" href="' . BASE_URL . '/?page=posts&job_id=' . $job['id'] . '">' . htmlspecialchars($job['name']) . '</a></li>';
}

$pageActions .= '</ul>
</div>';

// Handle actions
$action = isset($_GET['action']) ? $_GET['action'] : '';
$postId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$jobId = isset($_GET['job_id']) ? (int)$_GET['job_id'] : 0;
$view = isset($_GET['view']) ? $_GET['view'] : 'grid';

// Handle post deletion
if ($action === 'delete' && $postId > 0 && $_SERVER['REQUEST_METHOD'] === 'POST') {
    // Delete post images
    $db->delete('images', 'post_id = ?', [$postId]);

    // Delete post category relationships
    $db->delete('post_categories', 'post_id = ?', [$postId]);

    // Delete post tag relationships
    $db->delete('post_tags', 'post_id = ?', [$postId]);

    // Delete post
    $db->delete('posts', 'id = ?', [$postId]);

    // Redirect to posts list
    header('Location: ' . BASE_URL . '/?page=posts&success=' . urlencode('Post deleted successfully!'));
    exit;
}

// Display success/error messages
if (isset($_GET['success'])) {
    echo '<div class="alert alert-success alert-dismissible fade show" role="alert">
        ' . htmlspecialchars($_GET['success']) . '
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>';
}

if (isset($_GET['error'])) {
    echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
        ' . htmlspecialchars($_GET['error']) . '
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>';
}

// Display appropriate content based on action
switch ($action) {
    case 'view':
        // View post details
        $post = $db->getRow("SELECT p.*, j.name as job_name FROM posts p JOIN jobs j ON p.job_id = j.id WHERE p.id = ?", [$postId]);

        if (!$post) {
            echo '<div class="alert alert-danger">Post not found!</div>';
            echo '<p><a href="' . BASE_URL . '/?page=posts" class="btn btn-primary">Back to Posts</a></p>';
            break;
        }

        // Get post categories
        $categories = $db->query("
            SELECT c.*
            FROM categories c
            JOIN post_categories pc ON c.id = pc.category_id
            WHERE pc.post_id = ?
        ", [$post['id']]);

        // Get post tags
        $tags = $db->query("
            SELECT t.*
            FROM tags t
            JOIN post_tags pt ON t.id = pt.tag_id
            WHERE pt.post_id = ?
        ", [$post['id']]);

        // Get post images
        $images = $db->query("SELECT * FROM images WHERE post_id = ?", [$post['id']]);

        // Parse featured image
        $featuredImage = json_decode($post['featured_image'], true);
        ?>
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Post Details</h5>
                <div>
                    <a href="<?php echo $post['url']; ?>" target="_blank" class="btn btn-outline-primary btn-sm me-2">
                        <i class="fas fa-external-link-alt me-1"></i> Original
                    </a>
                    <?php if ($post['html_file'] && file_exists($post['html_file'])): ?>
                    <a href="<?php echo str_replace(BASE_PATH, BASE_URL, $post['html_file']); ?>" target="_blank" class="btn btn-outline-info btn-sm me-2">
                        <i class="fas fa-code me-1"></i> HTML
                    </a>
                    <?php endif; ?>
                    <?php if ($post['pdf_file'] && file_exists($post['pdf_file'])): ?>
                    <a href="<?php echo str_replace(BASE_PATH, BASE_URL, $post['pdf_file']); ?>" target="_blank" class="btn btn-outline-danger btn-sm me-2">
                        <i class="fas fa-file-pdf me-1"></i> PDF
                    </a>
                    <?php endif; ?>
                    <a href="<?php echo BASE_URL; ?>/process_post.php?id=<?php echo $post['id']; ?>" class="btn btn-success btn-sm">
                        <i class="fas fa-brain me-1"></i> AI Process
                    </a>
                </div>
            </div>
            <div class="card-body">
                <h1 class="mb-3"><?php echo htmlspecialchars($post['title']); ?></h1>

                <div class="mb-4">
                    <span class="badge bg-primary me-2">
                        <i class="fas fa-calendar-alt me-1"></i> <?php echo date('F j, Y', strtotime($post['date_published'])); ?>
                    </span>
                    <span class="badge bg-secondary me-2">
                        <i class="fas fa-tasks me-1"></i> <?php echo htmlspecialchars($post['job_name']); ?>
                    </span>
                    <?php foreach ($categories as $category): ?>
                    <span class="badge bg-info me-2">
                        <i class="fas fa-folder me-1"></i> <?php echo htmlspecialchars($category['name']); ?>
                    </span>
                    <?php endforeach; ?>
                </div>

                <?php if ($featuredImage): ?>
                <div class="mb-4">
                    <img src="<?php echo str_replace(BASE_PATH, BASE_URL, $featuredImage['local_path']); ?>" alt="<?php echo htmlspecialchars($featuredImage['alt'] ?? $post['title']); ?>" class="img-fluid rounded">
                </div>
                <?php endif; ?>

                <?php if ($post['excerpt']): ?>
                <div class="card mb-4">
                    <div class="card-body bg-light">
                        <h5>Excerpt</h5>
                        <p class="mb-0"><?php echo $post['excerpt']; ?></p>
                    </div>
                </div>
                <?php endif; ?>

                <div class="content mb-4">
                    <?php echo $post['content']; ?>
                </div>

                <?php if (!empty($tags)): ?>
                <div class="mb-4">
                    <h5>Tags</h5>
                    <?php foreach ($tags as $tag): ?>
                    <span class="badge bg-secondary me-1 mb-1"><?php echo htmlspecialchars($tag['name']); ?></span>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>

                <?php if (!empty($images)): ?>
                <div class="mb-4">
                    <h5>Images (<?php echo count($images); ?>)</h5>
                    <div class="row">
                        <?php foreach ($images as $image): ?>
                        <div class="col-md-2 col-sm-4 col-6 mb-3">
                            <a href="<?php echo str_replace(BASE_PATH, BASE_URL, $image['local_path']); ?>" target="_blank">
                                <img src="<?php echo str_replace(BASE_PATH, BASE_URL, $image['local_path']); ?>" alt="<?php echo htmlspecialchars($image['alt']); ?>" class="img-thumbnail">
                            </a>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>

                <div class="d-flex justify-content-between mt-4">
                    <a href="<?php echo BASE_URL; ?>/?page=posts" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Back to Posts
                    </a>
                    <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                        <i class="fas fa-trash me-1"></i> Delete Post
                    </button>
                </div>

                <!-- Delete Modal -->
                <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                Are you sure you want to delete the post "<?php echo htmlspecialchars($post['title']); ?>"?
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <form method="post" action="<?php echo BASE_URL; ?>/?page=posts&action=delete&id=<?php echo $post['id']; ?>">
                                    <button type="submit" class="btn btn-danger">Delete</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
        break;

    default:
        // Posts list
        // Build query
        $sql = "SELECT p.*, j.name as job_name FROM posts p JOIN jobs j ON p.job_id = j.id";
        $params = [];

        if ($jobId > 0) {
            $sql .= " WHERE p.job_id = ?";
            $params[] = $jobId;
        }

        $sql .= " ORDER BY p.date_published DESC";

        // Get posts
        $posts = $db->query($sql, $params);

        // View toggle
        echo '<div class="mb-3 d-flex justify-content-between align-items-center">
            <div>
                <span class="me-2">View:</span>
                <div class="btn-group" role="group">
                    <a href="' . BASE_URL . '/?page=posts' . ($jobId ? '&job_id=' . $jobId : '') . '&view=grid" class="btn btn-sm ' . ($view !== 'list' ? 'btn-primary' : 'btn-outline-primary') . '">
                        <i class="fas fa-th-large"></i> Grid
                    </a>
                    <a href="' . BASE_URL . '/?page=posts' . ($jobId ? '&job_id=' . $jobId : '') . '&view=list" class="btn btn-sm ' . ($view === 'list' ? 'btn-primary' : 'btn-outline-primary') . '">
                        <i class="fas fa-list"></i> List
                    </a>
                </div>
            </div>
            <div>
                <span class="badge bg-primary">' . count($posts) . ' Posts</span>
            </div>
        </div>';

        if (empty($posts)) {
            echo '<div class="alert alert-info">No posts found. Run a job to grab content.</div>';
        } elseif ($view === 'list') {
            // List view
            ?>
            <div class="card border-0 shadow-sm">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Title</th>
                                    <th>Job</th>
                                    <th>Date Published</th>
                                    <th>Images</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($posts as $post): ?>
                                <tr>
                                    <td>
                                        <a href="<?php echo BASE_URL; ?>/?page=posts&action=view&id=<?php echo $post['id']; ?>">
                                            <?php echo htmlspecialchars($post['title']); ?>
                                        </a>
                                    </td>
                                    <td><?php echo htmlspecialchars($post['job_name']); ?></td>
                                    <td><?php echo date('M d, Y', strtotime($post['date_published'])); ?></td>
                                    <td>
                                        <?php
                                        $imageCount = $db->getValue("SELECT COUNT(*) FROM images WHERE post_id = ?", [$post['id']]);
                                        echo $imageCount;
                                        ?>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="<?php echo BASE_URL; ?>/?page=posts&action=view&id=<?php echo $post['id']; ?>" class="btn btn-sm btn-primary" title="View Post">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo BASE_URL; ?>/process_post.php?id=<?php echo $post['id']; ?>" class="btn btn-sm btn-success" title="AI Process Post">
                                                <i class="fas fa-brain"></i>
                                            </a>
                                            <?php if ($post['html_file'] && file_exists($post['html_file'])): ?>
                                            <a href="<?php echo str_replace(BASE_PATH, BASE_URL, $post['html_file']); ?>" target="_blank" class="btn btn-sm btn-info" title="View HTML">
                                                <i class="fas fa-code"></i>
                                            </a>
                                            <?php endif; ?>
                                            <?php if ($post['pdf_file'] && file_exists($post['pdf_file'])): ?>
                                            <a href="<?php echo str_replace(BASE_PATH, BASE_URL, $post['pdf_file']); ?>" target="_blank" class="btn btn-sm btn-danger" title="View PDF">
                                                <i class="fas fa-file-pdf"></i>
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php
        } else {
            // Grid view
            ?>
            <div class="row">
                <?php foreach ($posts as $post): ?>
                <div class="col-md-4 mb-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <?php
                        $featuredImage = json_decode($post['featured_image'], true);
                        $imagePath = $featuredImage ? str_replace(BASE_PATH, BASE_URL, $featuredImage['local_path']) : BASE_URL . '/assets/images/placeholder.jpg';
                        ?>
                        <img src="<?php echo $imagePath; ?>" class="card-img-top" alt="<?php echo htmlspecialchars($post['title']); ?>" style="height: 200px; object-fit: cover;">
                        <div class="card-body">
                            <h5 class="card-title"><?php echo htmlspecialchars($post['title']); ?></h5>
                            <p class="card-text small text-muted">
                                <i class="fas fa-calendar-alt me-1"></i> <?php echo date('M d, Y', strtotime($post['date_published'])); ?>
                                <br>
                                <i class="fas fa-tasks me-1"></i> <?php echo htmlspecialchars($post['job_name']); ?>
                                <br>
                                <i class="fas fa-images me-1"></i>
                                <?php
                                $imageCount = $db->getValue("SELECT COUNT(*) FROM images WHERE post_id = ?", [$post['id']]);
                                echo $imageCount . ' images';
                                ?>
                            </p>
                            <div class="d-flex justify-content-between mt-3">
                                <a href="<?php echo BASE_URL; ?>/?page=posts&action=view&id=<?php echo $post['id']; ?>" class="btn btn-sm btn-primary">
                                    <i class="fas fa-eye me-1"></i> View
                                </a>
                                <a href="<?php echo BASE_URL; ?>/process_post.php?id=<?php echo $post['id']; ?>" class="btn btn-sm btn-success">
                                    <i class="fas fa-brain me-1"></i> AI Process
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php
        }
        break;
}
?>
