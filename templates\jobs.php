<?php
// Initialize JobManager
$jobManager = new JobManager($db);

// Set page title
$pageTitle = 'Jobs';

// Set page actions
$pageActions = '<a href="' . BASE_URL . '/?page=jobs&action=new" class="btn btn-primary">
    <i class="fas fa-plus me-1"></i> New Job
</a>';

// Handle actions
$action = isset($_GET['action']) ? $_GET['action'] : '';
$jobId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($action) {
        case 'new':
        case 'edit':
            // Create or update job
            $jobData = [
                'name' => $_POST['name'] ?? '',
                'url' => $_POST['url'] ?? '',
                'type' => $_POST['type'] ?? 'wordpress',
                'posts_per_run' => (int)($_POST['posts_per_run'] ?? 10),
                'category' => $_POST['category'] ?? null,
                'after_date' => $_POST['after_date'] ?? null,
                'before_date' => $_POST['before_date'] ?? null,
                'schedule_type' => $_POST['schedule_type'] ?? null,
                'disable_embed' => isset($_POST['disable_embed']) ? 1 : 0
            ];

            if ($action === 'new') {
                $jobId = $jobManager->createJob($jobData);
                $successMessage = 'Job created successfully!';
            } else {
                $jobManager->updateJob($jobId, $jobData);
                $successMessage = 'Job updated successfully!';
            }

            // Redirect to job list
            header('Location: ' . BASE_URL . '/?page=jobs&success=' . urlencode($successMessage));
            exit;
            break;

        case 'delete':
            // Delete job with all related data
            $result = $jobManager->deleteJob($jobId, true);

            if ($result['success']) {
                $message = "Job '{$result['job_name']}' deleted successfully! ";
                $message .= "Deleted {$result['deleted_posts']} posts, {$result['deleted_images']} images, and {$result['deleted_files']} files.";
                header('Location: ' . BASE_URL . '/?page=jobs&success=' . urlencode($message));
            } else {
                header('Location: ' . BASE_URL . '/?page=jobs&error=' . urlencode('Error deleting job: ' . $result['error']));
            }
            exit;
            break;
    }
}

// Handle run job action
if ($action === 'run' && $jobId > 0) {
    // Get job data
    $job = $jobManager->getJob($jobId);

    if (!$job) {
        $errorMessage = 'Job not found';
        header('Location: ' . BASE_URL . '/?page=jobs&error=' . urlencode($errorMessage));
        exit;
    }

    // Show progress page
    ?>
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Running Job: <?php echo htmlspecialchars($job['name']); ?></h5>
            <span class="badge bg-warning" id="job-status">Running</span>
        </div>
        <div class="card-body">
            <div class="mb-4">
                <p><strong>URL:</strong> <a href="<?php echo htmlspecialchars($job['url']); ?>" target="_blank"><?php echo htmlspecialchars($job['url']); ?></a></p>
                <p><strong>Type:</strong> <?php echo $job['type']; ?></p>
                <p><strong>Posts per run:</strong> <?php echo $job['posts_per_run']; ?></p>
            </div>

            <!-- Progress bar -->
            <div class="progress mb-3" style="height: 25px;">
                <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
            </div>

            <!-- Progress stats -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card border-primary">
                        <div class="card-body text-center">
                            <h5 class="card-title">Processed</h5>
                            <p id="processed-count" class="card-text display-6">0</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-success">
                        <div class="card-body text-center">
                            <h5 class="card-title">New</h5>
                            <p id="new-count" class="card-text display-6">0</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-warning">
                        <div class="card-body text-center">
                            <h5 class="card-title">Updated</h5>
                            <p id="updated-count" class="card-text display-6">0</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-secondary">
                        <div class="card-body text-center">
                            <h5 class="card-title">Skipped</h5>
                            <p id="skipped-count" class="card-text display-6">0</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Current post -->
            <div id="current-post" class="alert alert-info mb-3" style="display: none;">
                <strong>Currently processing:</strong> <span id="current-post-title"></span>
            </div>

            <!-- Log -->
            <div class="card bg-light">
                <div class="card-header">
                    <h6 class="mb-0">Job Log</h6>
                </div>
                <div class="card-body">
                    <pre id="job-log" class="mb-0" style="max-height: 200px; overflow-y: auto;"></pre>
                </div>
            </div>

            <div class="mt-4">
                <a href="<?php echo BASE_URL; ?>/?page=jobs" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Back to Jobs
                </a>
            </div>
        </div>
    </div>

    <script>
        // Function to update progress
        function updateProgress() {
            fetch("<?php echo BASE_URL; ?>/get_job_progress.php?job_id=<?php echo $jobId; ?>")
                .then(response => response.json())
                .then(data => {
                    if (data.status === "running") {
                        // Update progress bar
                        const progress = data.progress || {};
                        const progressPercent = progress.progress || 0;
                        document.getElementById("progress-bar").style.width = progressPercent + "%";
                        document.getElementById("progress-bar").textContent = progressPercent + "%";
                        document.getElementById("progress-bar").setAttribute("aria-valuenow", progressPercent);

                        // Update counters
                        document.getElementById("processed-count").textContent = progress.processed || 0;
                        document.getElementById("new-count").textContent = progress.new || 0;
                        document.getElementById("updated-count").textContent = progress.updated || 0;
                        document.getElementById("skipped-count").textContent = progress.skipped || 0;

                        // Update current post
                        if (progress.post_title) {
                            document.getElementById("current-post").style.display = "block";
                            document.getElementById("current-post-title").textContent = progress.post_title;

                            // Add to log
                            const logElement = document.getElementById("job-log");
                            logElement.textContent += "Processing: " + progress.post_title + "\n";
                            logElement.scrollTop = logElement.scrollHeight;
                        }

                        // Continue polling
                        setTimeout(updateProgress, 1000);
                    } else if (data.status === "completed") {
                        // Job completed, redirect to view page with stats
                        window.location.href = "<?php echo BASE_URL; ?>/?page=jobs&action=view&id=<?php echo $jobId; ?>&success=" + encodeURIComponent("Job completed successfully!") + "&show_stats=1";
                    } else if (data.status === "failed") {
                        // Job failed, redirect to view page with error
                        window.location.href = "<?php echo BASE_URL; ?>/?page=jobs&action=view&id=<?php echo $jobId; ?>&error=" + encodeURIComponent("Job failed: " + (data.error || "Unknown error"));
                    }
                })
                .catch(error => {
                    console.error("Error fetching progress:", error);
                    // Continue polling even on error
                    setTimeout(updateProgress, 2000);
                });
        }

        // Start progress updates
        setTimeout(updateProgress, 1000);
    </script>
    <?php

    // Run the job directly instead of using AJAX
    // This ensures the job actually runs
    $result = $jobManager->runJob($jobId);

    // Store stats in session for popup
    if ($result['success']) {
        $_SESSION['job_stats'] = $result['stats'] ?? null;
        $_SESSION['job_id'] = $jobId;
        $_SESSION['job_posts_count'] = $result['posts_count'];

        // Redirect to view page with success message
        header('Location: ' . BASE_URL . '/?page=jobs&action=view&id=' . $jobId . '&success=' . urlencode('Job ran successfully! Grabbed ' . $result['posts_count'] . ' posts.') . '&show_stats=1');
    } else {
        // Redirect to view page with error message
        header('Location: ' . BASE_URL . '/?page=jobs&action=view&id=' . $jobId . '&error=' . urlencode('Job failed: ' . $result['error']));
    }
    exit;
}

// Display success/error messages
if (isset($_GET['success'])) {
    echo '<div class="alert alert-success alert-dismissible fade show" role="alert">
        ' . htmlspecialchars($_GET['success']) . '
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>';
}

if (isset($_GET['error'])) {
    echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
        ' . htmlspecialchars($_GET['error']) . '
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>';
}

// Display appropriate content based on action
switch ($action) {
    case 'new':
        // New job form
        ?>
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="mb-0">Create New Job</h5>
            </div>
            <div class="card-body">
                <form method="post" action="<?php echo BASE_URL; ?>/?page=jobs&action=new">
                    <div class="mb-3">
                        <label for="name" class="form-label">Job Name</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>

                    <div class="mb-3">
                        <label for="url" class="form-label">URL</label>
                        <input type="url" class="form-control" id="url" name="url" required>
                        <div class="form-text">Enter the WordPress site URL or sitemap URL</div>
                    </div>

                    <div class="mb-3">
                        <label for="type" class="form-label">Type</label>
                        <select class="form-select" id="type" name="type">
                            <option value="wordpress">WordPress</option>
                            <option value="sitemap">Sitemap</option>
                        </select>
                        <div class="form-text">The system will try to auto-detect the type, but you can specify it manually</div>
                    </div>

                    <div class="mb-3">
                        <label for="posts_per_run" class="form-label">Posts Per Run</label>
                        <input type="number" class="form-control" id="posts_per_run" name="posts_per_run" value="10" min="1" max="100">
                        <div class="form-text">Number of posts to grab each time the job runs</div>
                    </div>

                    <div class="mb-3">
                        <label for="category" class="form-label">Category (Optional)</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="category" name="category">
                            <button class="btn btn-outline-secondary" type="button" id="fetchCategories">Fetch Categories</button>
                        </div>
                        <div class="form-text">For WordPress: category ID or slug. For sitemap: leave empty</div>
                        <div id="categoriesList" class="mt-2" style="display: none;">
                            <select class="form-select" id="categorySelect">
                                <option value="">Select a category</option>
                            </select>
                        </div>
                    </div>

                    <script>
                        document.addEventListener('DOMContentLoaded', function() {
                            const fetchCategoriesBtn = document.getElementById('fetchCategories');
                            const urlInput = document.getElementById('url');
                            const categoryInput = document.getElementById('category');
                            const categoriesList = document.getElementById('categoriesList');
                            const categorySelect = document.getElementById('categorySelect');

                            fetchCategoriesBtn.addEventListener('click', function() {
                                const url = urlInput.value.trim();
                                if (!url) {
                                    alert('Please enter a WordPress URL first');
                                    return;
                                }

                                // Show loading state
                                fetchCategoriesBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Loading...';
                                fetchCategoriesBtn.disabled = true;

                                // Fetch categories via AJAX
                                fetch('<?php echo BASE_URL; ?>/fetch_categories.php?url=' + encodeURIComponent(url))
                                    .then(response => response.json())
                                    .then(data => {
                                        // Reset button
                                        fetchCategoriesBtn.innerHTML = 'Fetch Categories';
                                        fetchCategoriesBtn.disabled = false;

                                        if (data.success) {
                                            // Clear previous options
                                            categorySelect.innerHTML = '<option value="">Select a category</option>';

                                            // Add categories to select
                                            data.categories.forEach(category => {
                                                const option = document.createElement('option');
                                                option.value = category.id;
                                                option.textContent = category.name + ' (' + category.count + ' posts)';
                                                option.dataset.slug = category.slug;
                                                categorySelect.appendChild(option);
                                            });

                                            // Show categories list
                                            categoriesList.style.display = 'block';

                                            // Add event listener to select
                                            categorySelect.addEventListener('change', function() {
                                                const selectedOption = this.options[this.selectedIndex];
                                                if (selectedOption.value) {
                                                    categoryInput.value = selectedOption.value;
                                                }
                                            });
                                        } else {
                                            alert('Error: ' + data.error);
                                        }
                                    })
                                    .catch(error => {
                                        fetchCategoriesBtn.innerHTML = 'Fetch Categories';
                                        fetchCategoriesBtn.disabled = false;
                                        alert('Error fetching categories: ' + error);
                                    });
                            });
                        });
                    </script>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="after_date" class="form-label">After Date (Optional)</label>
                                <input type="date" class="form-control" id="after_date" name="after_date">
                                <div class="form-text">Only grab posts published after this date</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="before_date" class="form-label">Before Date (Optional)</label>
                                <input type="date" class="form-control" id="before_date" name="before_date">
                                <div class="form-text">Only grab posts published before this date</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="disable_embed" name="disable_embed" value="1">
                        <label class="form-check-label" for="disable_embed">Disable _embed parameter</label>
                        <div class="form-text">Try this if you're having trouble grabbing posts from a WordPress site</div>
                    </div>

                    <div class="mb-3">
                        <label for="schedule_type" class="form-label">Schedule (Optional)</label>
                        <select class="form-select" id="schedule_type" name="schedule_type">
                            <option value="">No Schedule</option>
                            <option value="hourly">Hourly</option>
                            <option value="daily">Daily</option>
                            <option value="weekly">Weekly</option>
                            <option value="monthly">Monthly</option>
                        </select>
                        <div class="form-text">Automatically run this job on a schedule</div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="<?php echo BASE_URL; ?>/?page=jobs" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i> Back to Jobs
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> Create Job
                        </button>
                    </div>
                </form>
            </div>
        </div>
        <?php
        break;

    case 'edit':
        // Edit job form
        $job = $jobManager->getJob($jobId);

        if (!$job) {
            echo '<div class="alert alert-danger">Job not found!</div>';
            echo '<p><a href="' . BASE_URL . '/?page=jobs" class="btn btn-primary">Back to Jobs</a></p>';
            break;
        }
        ?>
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="mb-0">Edit Job: <?php echo htmlspecialchars($job['name']); ?></h5>
            </div>
            <div class="card-body">
                <form method="post" action="<?php echo BASE_URL; ?>/?page=jobs&action=edit&id=<?php echo $job['id']; ?>">
                    <div class="mb-3">
                        <label for="name" class="form-label">Job Name</label>
                        <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($job['name']); ?>" required>
                    </div>

                    <div class="mb-3">
                        <label for="url" class="form-label">URL</label>
                        <input type="url" class="form-control" id="url" name="url" value="<?php echo htmlspecialchars($job['url']); ?>" required>
                        <div class="form-text">Enter the WordPress site URL or sitemap URL</div>
                    </div>

                    <div class="mb-3">
                        <label for="type" class="form-label">Type</label>
                        <select class="form-select" id="type" name="type">
                            <option value="wordpress" <?php echo $job['type'] === 'wordpress' ? 'selected' : ''; ?>>WordPress</option>
                            <option value="sitemap" <?php echo $job['type'] === 'sitemap' ? 'selected' : ''; ?>>Sitemap</option>
                        </select>
                        <div class="form-text">The system will try to auto-detect the type, but you can specify it manually</div>
                    </div>

                    <div class="mb-3">
                        <label for="posts_per_run" class="form-label">Posts Per Run</label>
                        <input type="number" class="form-control" id="posts_per_run" name="posts_per_run" value="<?php echo $job['posts_per_run']; ?>" min="1" max="100">
                        <div class="form-text">Number of posts to grab each time the job runs</div>
                    </div>

                    <div class="mb-3">
                        <label for="category" class="form-label">Category (Optional)</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="category" name="category" value="<?php echo htmlspecialchars($job['category'] ?? ''); ?>">
                            <button class="btn btn-outline-secondary" type="button" id="fetchCategories">Fetch Categories</button>
                        </div>
                        <div class="form-text">For WordPress: category ID or slug. For sitemap: leave empty</div>
                        <div id="categoriesList" class="mt-2" style="display: none;">
                            <select class="form-select" id="categorySelect">
                                <option value="">Select a category</option>
                            </select>
                        </div>
                    </div>

                    <script>
                        document.addEventListener('DOMContentLoaded', function() {
                            const fetchCategoriesBtn = document.getElementById('fetchCategories');
                            const urlInput = document.getElementById('url');
                            const categoryInput = document.getElementById('category');
                            const categoriesList = document.getElementById('categoriesList');
                            const categorySelect = document.getElementById('categorySelect');

                            fetchCategoriesBtn.addEventListener('click', function() {
                                const url = urlInput.value.trim();
                                if (!url) {
                                    alert('Please enter a WordPress URL first');
                                    return;
                                }

                                // Show loading state
                                fetchCategoriesBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Loading...';
                                fetchCategoriesBtn.disabled = true;

                                // Fetch categories via AJAX
                                fetch('<?php echo BASE_URL; ?>/fetch_categories.php?url=' + encodeURIComponent(url))
                                    .then(response => response.json())
                                    .then(data => {
                                        // Reset button
                                        fetchCategoriesBtn.innerHTML = 'Fetch Categories';
                                        fetchCategoriesBtn.disabled = false;

                                        if (data.success) {
                                            // Clear previous options
                                            categorySelect.innerHTML = '<option value="">Select a category</option>';

                                            // Add categories to select
                                            data.categories.forEach(category => {
                                                const option = document.createElement('option');
                                                option.value = category.id;
                                                option.textContent = category.name + ' (' + category.count + ' posts)';
                                                option.dataset.slug = category.slug;
                                                categorySelect.appendChild(option);
                                            });

                                            // Show categories list
                                            categoriesList.style.display = 'block';

                                            // Add event listener to select
                                            categorySelect.addEventListener('change', function() {
                                                const selectedOption = this.options[this.selectedIndex];
                                                if (selectedOption.value) {
                                                    categoryInput.value = selectedOption.value;
                                                }
                                            });
                                        } else {
                                            alert('Error: ' + data.error);
                                        }
                                    })
                                    .catch(error => {
                                        fetchCategoriesBtn.innerHTML = 'Fetch Categories';
                                        fetchCategoriesBtn.disabled = false;
                                        alert('Error fetching categories: ' + error);
                                    });
                            });
                        });
                    </script>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="after_date" class="form-label">After Date (Optional)</label>
                                <input type="date" class="form-control" id="after_date" name="after_date" value="<?php echo $job['after_date'] ?? ''; ?>">
                                <div class="form-text">Only grab posts published after this date</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="before_date" class="form-label">Before Date (Optional)</label>
                                <input type="date" class="form-control" id="before_date" name="before_date" value="<?php echo $job['before_date'] ?? ''; ?>">
                                <div class="form-text">Only grab posts published before this date</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="disable_embed" name="disable_embed" value="1" <?php echo isset($job['disable_embed']) && $job['disable_embed'] ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="disable_embed">Disable _embed parameter</label>
                        <div class="form-text">Try this if you're having trouble grabbing posts from a WordPress site</div>
                    </div>

                    <div class="mb-3">
                        <label for="schedule_type" class="form-label">Schedule (Optional)</label>
                        <select class="form-select" id="schedule_type" name="schedule_type">
                            <option value="" <?php echo empty($job['schedule_type']) ? 'selected' : ''; ?>>No Schedule</option>
                            <option value="hourly" <?php echo $job['schedule_type'] === 'hourly' ? 'selected' : ''; ?>>Hourly</option>
                            <option value="daily" <?php echo $job['schedule_type'] === 'daily' ? 'selected' : ''; ?>>Daily</option>
                            <option value="weekly" <?php echo $job['schedule_type'] === 'weekly' ? 'selected' : ''; ?>>Weekly</option>
                            <option value="monthly" <?php echo $job['schedule_type'] === 'monthly' ? 'selected' : ''; ?>>Monthly</option>
                        </select>
                        <div class="form-text">Automatically run this job on a schedule</div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="<?php echo BASE_URL; ?>/?page=jobs" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i> Back to Jobs
                        </a>
                        <div>
                            <a href="<?php echo BASE_URL; ?>/?page=jobs&action=run&id=<?php echo $job['id']; ?>" class="btn btn-success me-2">
                                <i class="fas fa-play me-1"></i> Run Now
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Save Changes
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <?php
        break;

    case 'view':
        // View job details
        $job = $jobManager->getJob($jobId);

        if (!$job) {
            echo '<div class="alert alert-danger">Job not found!</div>';
            echo '<p><a href="' . BASE_URL . '/?page=jobs" class="btn btn-primary">Back to Jobs</a></p>';
            break;
        }

        // Get job posts
        $posts = $db->query("SELECT * FROM posts WHERE job_id = ? ORDER BY date_published DESC", [$job['id']]);
        ?>
        <?php
        // Show statistics popup if requested
        if (isset($_GET['show_stats']) && $_GET['show_stats'] == 1 && isset($_SESSION['job_stats']) && $_SESSION['job_id'] == $jobId) {
            $stats = $_SESSION['job_stats'];
            $postsCount = $_SESSION['job_posts_count'];
            ?>
            <div class="modal fade" id="statsModal" tabindex="-1" aria-labelledby="statsModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="statsModalLabel">Job Statistics</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="card border-0 bg-light mb-3">
                                <div class="card-body">
                                    <h6 class="card-title">Summary</h6>
                                    <p class="card-text mb-0">Total posts grabbed: <strong><?php echo $postsCount; ?></strong></p>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="card border-0 bg-success text-white h-100">
                                        <div class="card-body text-center">
                                            <h1 class="display-4"><?php echo $stats['new']; ?></h1>
                                            <p class="card-text">New Posts</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="card border-0 bg-info text-white h-100">
                                        <div class="card-body text-center">
                                            <h1 class="display-4"><?php echo $stats['updated']; ?></h1>
                                            <p class="card-text">Updated Posts</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="card border-0 bg-secondary text-white h-100">
                                        <div class="card-body text-center">
                                            <h1 class="display-4"><?php echo $stats['skipped']; ?></h1>
                                            <p class="card-text">Skipped Posts</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="card border-0 bg-primary text-white h-100">
                                        <div class="card-body text-center">
                                            <h1 class="display-4"><?php echo $stats['execution_time']; ?>s</h1>
                                            <p class="card-text">Execution Time</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>

            <script>
                // Show the modal when the page loads
                document.addEventListener('DOMContentLoaded', function() {
                    var statsModal = new bootstrap.Modal(document.getElementById('statsModal'));
                    statsModal.show();
                });
            </script>
            <?php
            // Clear the session data
            unset($_SESSION['job_stats']);
            unset($_SESSION['job_id']);
            unset($_SESSION['job_posts_count']);
        }
        ?>

        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Job Details: <?php echo htmlspecialchars($job['name']); ?></h5>
                <div>
                    <a href="<?php echo BASE_URL; ?>/?page=jobs&action=run&id=<?php echo $job['id']; ?>" class="btn btn-success btn-sm me-2">
                        <i class="fas fa-play me-1"></i> Run Now
                    </a>
                    <a href="<?php echo BASE_URL; ?>/?page=jobs&action=edit&id=<?php echo $job['id']; ?>" class="btn btn-primary btn-sm">
                        <i class="fas fa-edit me-1"></i> Edit
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table">
                            <tr>
                                <th>URL:</th>
                                <td><a href="<?php echo htmlspecialchars($job['url']); ?>" target="_blank"><?php echo htmlspecialchars($job['url']); ?></a></td>
                            </tr>
                            <tr>
                                <th>Type:</th>
                                <td>
                                    <?php if ($job['type'] === 'wordpress'): ?>
                                    <span class="badge bg-primary">WordPress</span>
                                    <?php else: ?>
                                    <span class="badge bg-info">Sitemap</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <th>Status:</th>
                                <td>
                                    <?php
                                    switch ($job['status']) {
                                        case 'pending':
                                            echo '<span class="badge bg-secondary">Pending</span>';
                                            break;
                                        case 'running':
                                            echo '<span class="badge bg-warning">Running</span>';
                                            break;
                                        case 'completed':
                                            echo '<span class="badge bg-success">Completed</span>';
                                            break;
                                        case 'failed':
                                            echo '<span class="badge bg-danger">Failed</span>';
                                            break;
                                    }
                                    ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table">
                            <tr>
                                <th>Posts Per Run:</th>
                                <td><?php echo $job['posts_per_run']; ?></td>
                            </tr>
                            <?php if ($job['type'] === 'wordpress'): ?>
                            <tr>
                                <th>Disable _embed:</th>
                                <td><?php echo isset($job['disable_embed']) && $job['disable_embed'] ? 'Yes' : 'No'; ?></td>
                            </tr>
                            <?php endif; ?>
                            <tr>
                                <th>Schedule:</th>
                                <td><?php echo $job['schedule_type'] ? ucfirst($job['schedule_type']) : 'None'; ?></td>
                            </tr>
                            <tr>
                                <th>Last Run:</th>
                                <td><?php echo $job['last_run'] ? date('M d, Y H:i', strtotime($job['last_run'])) : 'Never'; ?></td>
                            </tr>
                        </table>
                    </div>
                </div>

                <?php if ($job['error']): ?>
                <div class="alert alert-danger mt-3">
                    <strong>Last Error:</strong> <?php echo htmlspecialchars($job['error']); ?>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="mb-0">Posts (<?php echo count($posts); ?>)</h5>
            </div>
            <div class="card-body p-0">
                <?php if (empty($posts)): ?>
                <div class="p-4 text-center">
                    <p class="text-muted mb-0">No posts found for this job. Run the job to grab content.</p>
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Date Published</th>
                                <th>Images</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($posts as $post): ?>
                            <tr>
                                <td>
                                    <a href="<?php echo BASE_URL; ?>/?page=posts&action=view&id=<?php echo $post['id']; ?>">
                                        <?php echo htmlspecialchars($post['title']); ?>
                                    </a>
                                </td>
                                <td><?php echo date('M d, Y', strtotime($post['date_published'])); ?></td>
                                <td>
                                    <?php
                                    $imageCount = $db->getValue("SELECT COUNT(*) FROM images WHERE post_id = ?", [$post['id']]);
                                    echo $imageCount;
                                    ?>
                                </td>
                                <td>
                                    <a href="<?php echo BASE_URL; ?>/?page=posts&action=view&id=<?php echo $post['id']; ?>" class="btn btn-sm btn-outline-primary me-1">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?php echo BASE_URL; ?>/?page=process&id=<?php echo $post['id']; ?>" class="btn btn-sm btn-outline-success">
                                        <i class="fas fa-cogs"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php
        break;

    default:
        // Job list
        $jobs = $jobManager->getJobs();
        ?>
        <div class="card border-0 shadow-sm">
            <div class="card-body p-0">
                <?php if (empty($jobs)): ?>
                <div class="p-4 text-center">
                    <p class="text-muted mb-4">No jobs found. Create your first job to start grabbing content.</p>
                    <a href="<?php echo BASE_URL; ?>/?page=jobs&action=new" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i> Create New Job
                    </a>
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>URL</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Schedule</th>
                                <th>Last Run</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($jobs as $job): ?>
                            <tr>
                                <td>
                                    <a href="<?php echo BASE_URL; ?>/?page=jobs&action=view&id=<?php echo $job['id']; ?>">
                                        <?php echo htmlspecialchars($job['name']); ?>
                                    </a>
                                </td>
                                <td>
                                    <a href="<?php echo htmlspecialchars($job['url']); ?>" target="_blank" class="text-truncate d-inline-block" style="max-width: 200px;">
                                        <?php echo htmlspecialchars($job['url']); ?>
                                    </a>
                                </td>
                                <td>
                                    <?php if ($job['type'] === 'wordpress'): ?>
                                    <span class="badge bg-primary">WordPress</span>
                                    <?php else: ?>
                                    <span class="badge bg-info">Sitemap</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php
                                    switch ($job['status']) {
                                        case 'pending':
                                            echo '<span class="badge bg-secondary">Pending</span>';
                                            break;
                                        case 'running':
                                            echo '<span class="badge bg-warning">Running</span>';
                                            break;
                                        case 'completed':
                                            echo '<span class="badge bg-success">Completed</span>';
                                            break;
                                        case 'failed':
                                            echo '<span class="badge bg-danger">Failed</span>';
                                            break;
                                    }
                                    ?>
                                </td>
                                <td><?php echo $job['schedule_type'] ? ucfirst($job['schedule_type']) : 'None'; ?></td>
                                <td><?php echo $job['last_run'] ? date('M d, Y H:i', strtotime($job['last_run'])) : 'Never'; ?></td>
                                <td>
                                    <div class="btn-group">
                                        <a href="<?php echo BASE_URL; ?>/?page=jobs&action=run&id=<?php echo $job['id']; ?>" class="btn btn-sm btn-success" title="Run Job">
                                            <i class="fas fa-play"></i>
                                        </a>
                                        <a href="<?php echo BASE_URL; ?>/?page=jobs&action=edit&id=<?php echo $job['id']; ?>" class="btn btn-sm btn-primary" title="Edit Job">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="<?php echo BASE_URL; ?>/?page=jobs&action=view&id=<?php echo $job['id']; ?>" class="btn btn-sm btn-info" title="View Job">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-danger" title="Delete Job" data-bs-toggle="modal" data-bs-target="#deleteModal<?php echo $job['id']; ?>">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>

                                    <!-- Delete Modal -->
                                    <div class="modal fade" id="deleteModal<?php echo $job['id']; ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?php echo $job['id']; ?>" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="deleteModalLabel<?php echo $job['id']; ?>">Confirm Delete</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    Are you sure you want to delete the job "<?php echo htmlspecialchars($job['name']); ?>"?
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                    <form method="post" action="<?php echo BASE_URL; ?>/?page=jobs&action=delete&id=<?php echo $job['id']; ?>">
                                                        <button type="submit" class="btn btn-danger">Delete</button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php
        break;
}
?>
